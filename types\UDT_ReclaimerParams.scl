TYPE UDT_ReclaimerParams :
STRUCT
    // 保护与联锁阈值
    beltSpeedMin_mps       : REAL := 0.2;      // 皮带最小速度
    slewEnable             : BOOL := TRUE;     // 回转允许
    luffEnable             : BOOL := TRUE;     // 俯仰允许
    travelEnable           : BOOL := TRUE;     // 行走允许

    // 时序（毫秒）
    t_Precheck_ms          : DINT := 500;      // 启动前自检
    t_StartSeq_ms          : DINT := 2000;     // 启动时序步间隔
    t_StopSeq_ms           : DINT := 1500;     // 停机时序步间隔
    t_FaultReset_ms        : DINT := 1000;     // 故障复位保持

    // 动作限位（示意）
    luffAngleMax_deg       : REAL := 65.0;
    luffAngleMin_deg       : REAL := 10.0;
    slewLeftLimit          : BOOL := FALSE;
    slewRightLimit         : BOOL := FALSE;
END_STRUCT;
END_TYPE

