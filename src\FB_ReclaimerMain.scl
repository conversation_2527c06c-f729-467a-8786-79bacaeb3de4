FUNCTION_BLOCK FB_ReclaimerMain
{ S7_Optimized_Access := 'TRUE' }
VAR_INPUT
    CmdStart        : BOOL;       // 运行启动
    CmdStop         : BOOL;       // 停止命令
    CmdReset        : BOOL;       // 故障复位

    BeltSpeed_mps   : REAL;       // 皮带速度反馈
    LuffAngle_deg   : REAL;       // 俯仰角度
    SlewLeftLimit   : BOOL;       // 回转左极限
    SlewRightLimit  : BOOL;       // 回转右极限

    CycleMs         : DINT := 100; // OB 周期（ms），用于软定时
END_VAR

VAR_OUTPUT
    Run             : BOOL;       // 运行中
    Fault           : BOOL;       // 故障总信号
    Ready           : BOOL;       // 准备就绪

    // 执行机构输出（示意）
    Out_BeltStart   : BOOL;
    Out_LuffEnable  : BOOL;
    Out_SlewEnable  : BOOL;
    Out_TravelEnable: BOOL;
END_VAR

VAR_IN_OUT
    Params          : UDT_ReclaimerParams; // 参数与阈值
END_VAR

VAR
    state           : INT := 0;    // 主状态机
    timer_ms        : DINT := 0;   // 软定时
    start_prev      : BOOL := FALSE;
    stop_prev       : BOOL := FALSE;
    reset_prev      : BOOL := FALSE;

    // 故障点
    flt_BeltLowSpd  : BOOL := FALSE;
    flt_LuffRange   : BOOL := FALSE;
    flt_SlewLimit   : BOOL := FALSE;
END_VAR

// 上升沿检测
VAR_TEMP
    rStart : BOOL;
    rStop  : BOOL;
    rReset : BOOL;
END_VAR

BEGIN

rStart := (CmdStart = TRUE) AND (start_prev = FALSE);
rStop  := (CmdStop  = TRUE) AND (stop_prev  = FALSE);
rReset := (CmdReset = TRUE) AND (reset_prev = FALSE);
start_prev := CmdStart;
stop_prev  := CmdStop;
reset_prev := CmdReset;

// 故障判据
flt_BeltLowSpd := (Run = TRUE) AND (BeltSpeed_mps < Params.beltSpeedMin_mps);
flt_LuffRange  := (LuffAngle_deg > Params.luffAngleMax_deg) OR (LuffAngle_deg < Params.luffAngleMin_deg);
flt_SlewLimit  := (SlewLeftLimit AND SlewRightLimit); // 同时极限视为异常
Fault := flt_BeltLowSpd OR flt_LuffRange OR flt_SlewLimit;

// 故障复位：保持时间
IF rReset THEN
    timer_ms := 0;
END_IF;
IF CmdReset THEN
    timer_ms := timer_ms + CycleMs;
    IF timer_ms >= Params.t_FaultReset_ms THEN
        flt_BeltLowSpd := FALSE;
        flt_LuffRange  := FALSE;
        flt_SlewLimit  := FALSE;
    END_IF;
END_IF;

// 主状态机
CASE state OF
    0: // Idle
        Run   := FALSE;
        Ready := (Fault = FALSE);
        Out_BeltStart    := FALSE;
        Out_LuffEnable   := FALSE;
        Out_SlewEnable   := FALSE;
        Out_TravelEnable := FALSE;
        timer_ms := 0;
        IF rStart AND (Fault = FALSE) THEN
            state := 10; // 进入自检
        END_IF;

    10: // Precheck
        Ready := FALSE;
        timer_ms := timer_ms + CycleMs;
        IF timer_ms >= Params.t_Precheck_ms THEN
            timer_ms := 0;
            state := 20;
        END_IF;
        IF rStop OR Fault THEN
            state := 0;
        END_IF;

    20: // Start sequence: 皮带
        Out_BeltStart := TRUE;
        timer_ms := timer_ms + CycleMs;
        IF timer_ms >= Params.t_StartSeq_ms THEN
            timer_ms := 0;
            state := 30;
        END_IF;
        IF rStop OR Fault THEN
            state := 90; // 异常转停机序列
        END_IF;

    30: // Start sequence: 释放动作使能
        Out_LuffEnable    := Params.luffEnable;
        Out_SlewEnable    := Params.slewEnable;
        Out_TravelEnable  := Params.travelEnable;
        Run := TRUE;
        state := 40;

    40: // Running
        IF rStop THEN
            timer_ms := 0;
            state := 80; // 正常停机
        ELSIF Fault THEN
            timer_ms := 0;
            state := 90; // 故障停机
        END_IF;

    80: // Stop sequence (normal)
        Out_LuffEnable    := FALSE;
        Out_SlewEnable    := FALSE;
        Out_TravelEnable  := FALSE;
        timer_ms := timer_ms + CycleMs;
        IF timer_ms >= Params.t_StopSeq_ms THEN
            Out_BeltStart := FALSE;
            Run := FALSE;
            state := 0;
        END_IF;

    90: // Stop sequence (fault or abort)
        Out_LuffEnable    := FALSE;
        Out_SlewEnable    := FALSE;
        Out_TravelEnable  := FALSE;
        Out_BeltStart     := FALSE;
        Run := FALSE;
        state := 0;

    ELSE
        state := 0;
END_CASE;

END_FUNCTION_BLOCK

